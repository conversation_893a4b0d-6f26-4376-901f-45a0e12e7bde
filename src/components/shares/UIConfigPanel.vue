<script setup lang="ts">
	import { computed } from "vue";
	import { useI18n } from "vue-i18n";
	import type { ShareUIConfig } from "@/api/shares";
	import { PlayIcon, DocumentDuplicateIcon } from "@heroicons/vue/24/outline";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: ShareUIConfig;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: ShareUIConfig): void;
	}>();

	const uiConfig = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const updateField = (field: keyof ShareUIConfig, value: any) => {
		uiConfig.value = {
			...uiConfig.value,
			[field]: value,
			// 确保 allow_view 和 show_workflow_graph 始终为 true
			allow_view: true,
			show_workflow_graph: true,
		};
	};

	const hasAnyPermission = computed(() => {
		return uiConfig.value.allow_run || uiConfig.value.allow_copy;
	});
</script>

<template>
	<div class="space-y-6">
		<!-- Permissions -->
		<div class="space-y-4">
			<h5 class="text-sm font-semibold text-slate-700 mb-3">
				{{ t("shares.permissions") }}
			</h5>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Allow Run -->
				<div class="bg-slate-50 rounded-xl p-4 border border-slate-200">
					<div class="flex items-center justify-between mb-2">
						<div class="flex items-center gap-3">
							<div
								class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"
							>
								<PlayIcon class="w-4 h-4 text-green-600" />
							</div>
							<div>
								<p class="font-medium text-slate-800">
									{{ t("shares.allow_run") }}
								</p>
								<p class="text-xs text-slate-500">
									{{ t("shares.allow_run_desc") }}
								</p>
							</div>
						</div>
						<input
							type="checkbox"
							class="toggle toggle-success"
							:checked="uiConfig.allow_run"
							@change="
								updateField(
									'allow_run',
									($event.target as HTMLInputElement).checked
								)
							"
						/>
					</div>
				</div>

				<!-- Allow Copy -->
				<div class="bg-slate-50 rounded-xl p-4 border border-slate-200">
					<div class="flex items-center justify-between mb-2">
						<div class="flex items-center gap-3">
							<div
								class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"
							>
								<DocumentDuplicateIcon class="w-4 h-4 text-purple-600" />
							</div>
							<div>
								<p class="font-medium text-slate-800">
									{{ t("shares.allow_copy") }}
								</p>
								<p class="text-xs text-slate-500">
									{{ t("shares.allow_copy_desc") }}
								</p>
							</div>
						</div>
						<input
							type="checkbox"
							class="toggle toggle-success"
							:checked="uiConfig.allow_copy"
							@change="
								updateField(
									'allow_copy',
									($event.target as HTMLInputElement).checked
								)
							"
						/>
					</div>
				</div>
			</div>
		</div>

		<!-- Permission Validation Alert -->
		<div v-if="!hasAnyPermission" class="bg-red-50 border border-red-200 rounded-xl p-4">
			<div class="flex items-center gap-3">
				<div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
					<svg
						class="w-4 h-4 text-red-600"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
						></path>
					</svg>
				</div>
				<div>
					<p class="font-medium text-red-800">
						{{ t("shares.permission_required") }}
					</p>
					<p class="text-sm text-red-600">
						{{ t("shares.at_least_one_permission") }}
					</p>
				</div>
			</div>
		</div>

		<!-- Welcome Message -->
		<div class="space-y-3">
			<h5 class="text-sm font-semibold text-slate-700">
				{{ t("shares.welcome_message") }}
			</h5>
			<div class="bg-slate-50 rounded-xl p-4 border border-slate-200">
				<textarea
					:value="uiConfig.welcome_message"
					@input="
						updateField(
							'welcome_message',
							($event.target as HTMLTextAreaElement).value
						)
					"
					class="textarea textarea-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all resize-none"
					:placeholder="t('shares.welcome_message_placeholder')"
					rows="4"
				></textarea>
				<p class="text-xs text-slate-500 mt-2">
					{{ t("shares.welcome_message_desc") }}
				</p>
			</div>
		</div>
	</div>
</template>
