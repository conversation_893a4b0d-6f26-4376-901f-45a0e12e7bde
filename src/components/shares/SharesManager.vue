<script setup lang="ts">
	import { ref, computed, watch } from "vue";
	import { useI18n } from "vue-i18n";
	import { Error, Success } from "@/utils/notify";
	import { SharesAPI, type WorkflowShare, type ShareStats } from "@/api/shares";
	import {
		XMarkIcon,
		ShareIcon,
		EyeIcon,
		PlayIcon,
		DocumentDuplicateIcon,
	} from "@heroicons/vue/24/outline";
	import CreateShareModal from "./CreateShareModal.vue";
	import EditShareModal from "./EditShareModal.vue";
	import ShareStatsModal from "./ShareStats.vue";
	import ShareLogs from "./ShareLogs.vue";
	import ShareLinkCopy from "./ShareLinkCopy.vue";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: boolean;
		workflowId: string;
		workflowName: string;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: boolean): void;
	}>();

	const show = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const shares = ref<WorkflowShare[]>([]);
	const loading = ref(false);
	const showCreateModal = ref(false);
	const showEditModal = ref(false);
	const showStatsModal = ref(false);
	const showLogsModal = ref(false);
	const selectedShare = ref<WorkflowShare | null>(null);
	const selectedShareStats = ref<ShareStats | null>(null);

	const loadShares = async () => {
		loading.value = true;
		try {
			const res = await SharesAPI.getWorkflowShares(props.workflowId);
			shares.value = res.shares;
		} catch (err) {
			console.error(err);
			Error(t("error"), t("shares.failed_to_load_shares"));
		} finally {
			loading.value = false;
		}
	};

	const handleDeleteShare = async (share: WorkflowShare) => {
		if (!confirm(t("shares.confirm_delete_share"))) return;

		try {
			await SharesAPI.deleteShare(props.workflowId, share.share_uuid);
			Success(t("success"), t("shares.delete_share_success"));
			await loadShares();
		} catch (err) {
			Error(t("error"), t("shares.delete_share_error"));
		}
	};

	const handleToggleShare = async (share: WorkflowShare) => {
		try {
			await SharesAPI.updateShare(props.workflowId, share.share_uuid, {
				is_enabled: !share.is_enabled,
			});
			Success(t("success"), t("shares.toggle_share_success"));
			await loadShares();
		} catch (err) {
			Error(t("error"), t("shares.toggle_share_error"));
		}
	};

	const handleViewStats = async (share: WorkflowShare) => {
		try {
			const res = await SharesAPI.getShareDetails(props.workflowId, share.share_uuid);
			selectedShare.value = res.share;
			selectedShareStats.value = res.stats;
			showStatsModal.value = true;
		} catch (err) {
			Error(t("error"), t("shares.failed_to_load_stats"));
		}
	};

	const handleViewLogs = (share: WorkflowShare) => {
		selectedShare.value = share;
		showLogsModal.value = true;
	};

	const handleEditShare = (share: WorkflowShare) => {
		selectedShare.value = share;
		showEditModal.value = true;
	};

	const handleCreateSuccess = () => {
		showCreateModal.value = false;
		loadShares();
	};

	const handleEditSuccess = () => {
		showEditModal.value = false;
		loadShares();
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString();
	};

	const isExpired = (share: WorkflowShare) => {
		if (!share.expires_at) return false;
		return new Date(share.expires_at) < new Date();
	};

	watch(
		() => show.value,
		(newVal) => {
			if (newVal) {
				loadShares();
			}
		}
	);
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div
				class="modal-box relative max-w-7xl h-[90vh] p-0 bg-gradient-to-br from-slate-50 to-blue-50/30"
			>
				<!-- 头部区域 -->
				<div
					class="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b border-slate-200 p-6"
				>
					<div class="flex justify-between items-center">
						<div class="flex items-center gap-3">
							<div
								class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"
							>
								<ShareIcon class="w-5 h-5 text-white" />
							</div>
							<div>
								<h3 class="font-bold text-2xl text-slate-800">
									{{ t("shares.workflow_shares") }}
								</h3>
								<p class="text-sm text-slate-600 mt-1">
									{{
										t("shares.manage_shares_for", {
											workflow: workflowName,
										})
									}}
								</p>
							</div>
						</div>
						<div class="flex items-center gap-3">
							<button
								class="btn-gradient-primary gap-2"
								@click="showCreateModal = true"
							>
								<ShareIcon class="w-4 h-4" />
								{{ t("shares.create_share") }}
							</button>
							<button
								class="btn btn-ghost btn-circle btn-sm hover:bg-slate-100"
								@click="show = false"
							>
								<XMarkIcon class="w-5 h-5" />
							</button>
						</div>
					</div>
				</div>

				<!-- 内容区域 -->
				<div class="p-6 h-[calc(100%-120px)] overflow-y-auto">
					<!-- 加载状态 -->
					<div v-if="loading" class="flex items-center justify-center py-16">
						<div class="flex flex-col items-center gap-4">
							<span
								class="loading loading-spinner loading-lg text-blue-500"
							></span>
							<p class="text-slate-600">{{ t("shares.loading") }}</p>
						</div>
					</div>

					<!-- 空状态 -->
					<div
						v-else-if="shares.length === 0"
						class="flex flex-col items-center justify-center py-16"
					>
						<div
							class="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mb-6"
						>
							<ShareIcon class="w-10 h-10 text-blue-500" />
						</div>
						<h4 class="text-xl font-semibold text-slate-800 mb-2">
							{{ t("shares.no_shares") }}
						</h4>
						<p class="text-slate-600 mb-6 text-center max-w-md">
							{{ t("shares.create_first_share") }}
						</p>
						<button
							class="btn-gradient-primary gap-2"
							@click="showCreateModal = true"
						>
							<ShareIcon class="w-4 h-4" />
							{{ t("shares.create_share") }}
						</button>
					</div>

					<!-- 分享列表 -->
					<div v-else class="grid gap-4">
						<div
							v-for="share in shares"
							:key="share.share_uuid"
							class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300 overflow-hidden"
						>
							<!-- 卡片头部 -->
							<div class="p-6 border-b border-slate-100">
								<div class="flex items-start justify-between">
									<div class="flex-1">
										<div class="flex items-center gap-3 mb-3">
											<h4 class="text-xl font-semibold text-slate-800">
												{{ share.title }}
											</h4>
											<span
												class="px-3 py-1 rounded-full text-xs font-medium"
												:class="
													share.is_enabled && !isExpired(share)
														? 'bg-green-100 text-green-700 border border-green-200'
														: 'bg-red-100 text-red-700 border border-red-200'
												"
											>
												{{
													share.is_enabled
														? isExpired(share)
															? t("shares.expired")
															: t("shares.active")
														: t("shares.disabled")
												}}
											</span>
											<span
												class="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200"
											>
												{{
													share.bill_to === "sharer"
														? t("shares.sharer_pays")
														: t("shares.runner_pays")
												}}
											</span>
										</div>
										<p class="text-slate-600 mb-4 leading-relaxed">
											{{
												share.description || t("shares.no_description")
											}}
										</p>
									</div>
									<ShareLinkCopy :share-uuid="share.share_uuid" />
								</div>
							</div>

							<!-- 卡片内容 -->
							<div class="p-6 pt-4">
								<!-- 权限信息 -->
								<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
									<div
										class="flex items-center gap-3 p-3 bg-slate-50 rounded-xl"
									>
										<div
											class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"
										>
											<EyeIcon class="w-4 h-4 text-blue-600" />
										</div>
										<div>
											<p class="text-sm font-medium text-slate-700">
												{{ t("shares.view") }}
											</p>
											<p class="text-xs text-slate-500">
												{{
													share.ui_config.allow_view
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</p>
										</div>
									</div>
									<div
										class="flex items-center gap-3 p-3 bg-slate-50 rounded-xl"
									>
										<div
											class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"
										>
											<PlayIcon class="w-4 h-4 text-green-600" />
										</div>
										<div>
											<p class="text-sm font-medium text-slate-700">
												{{ t("shares.run") }}
											</p>
											<p class="text-xs text-slate-500">
												{{
													share.ui_config.allow_run
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</p>
										</div>
									</div>
									<div
										class="flex items-center gap-3 p-3 bg-slate-50 rounded-xl"
									>
										<div
											class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"
										>
											<DocumentDuplicateIcon
												class="w-4 h-4 text-purple-600"
											/>
										</div>
										<div>
											<p class="text-sm font-medium text-slate-700">
												{{ t("shares.copy") }}
											</p>
											<p class="text-xs text-slate-500">
												{{
													share.ui_config.allow_copy
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</p>
										</div>
									</div>
								</div>

								<!-- 时间信息 -->
								<div
									class="flex items-center gap-6 text-sm text-slate-500 mb-6"
								>
									<span>
										{{ t("shares.created_at") }}:
										{{ formatDate(share.created_at) }}
									</span>
									<span v-if="share.expires_at">
										{{ t("shares.expires_at") }}:
										{{ formatDate(share.expires_at) }}
									</span>
								</div>

								<!-- 操作按钮 -->
								<div class="flex items-center gap-2 flex-wrap">
									<button
										class="btn-outline-primary btn-sm gap-1"
										@click="handleViewStats(share)"
									>
										{{ t("shares.stats") }}
									</button>
									<button
										class="btn-outline-primary btn-sm gap-1"
										@click="handleViewLogs(share)"
									>
										{{ t("shares.logs") }}
									</button>
									<button
										class="btn-modern btn-sm gap-1"
										@click="handleEditShare(share)"
									>
										{{ t("shares.edit") }}
									</button>
									<button
										class="btn-sm gap-1"
										:class="
											share.is_enabled
												? 'btn-outline-warning'
												: 'btn-outline-success'
										"
										@click="handleToggleShare(share)"
									>
										{{
											share.is_enabled
												? t("shares.disable")
												: t("shares.enable")
										}}
									</button>
									<button
										class="btn-outline-danger btn-sm gap-1"
										@click="handleDeleteShare(share)"
									>
										{{ t("shares.delete") }}
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="show = false"></label>
		</div>
	</Teleport>

	<!-- Create Share Modal -->
	<CreateShareModal
		v-model="showCreateModal"
		:workflow-id="workflowId"
		:workflow-name="workflowName"
		@create-success="handleCreateSuccess"
	/>

	<!-- Edit Share Modal -->
	<EditShareModal
		v-model="showEditModal"
		:workflow-id="workflowId"
		:share="selectedShare"
		@edit-success="handleEditSuccess"
	/>

	<!-- Stats Modal -->
	<ShareStatsModal
		v-model="showStatsModal"
		:share="selectedShare"
		:stats="selectedShareStats"
	/>

	<!-- Logs Modal -->
	<ShareLogs v-model="showLogsModal" :workflow-id="workflowId" :share="selectedShare" />
</template>
