<script setup lang="ts">
	import { ref, computed } from "vue";
	import { useI18n } from "vue-i18n";
	import { Error, Success } from "@/utils/notify";
	import {
		SharesAPI,
		type CreateShareRequest,
		DEFAULT_RATE_LIMIT,
		DEFAULT_UI_CONFIG,
	} from "@/api/shares";
	import { XMarkIcon, ShareIcon } from "@heroicons/vue/24/outline";
	import RateLimitConfig from "./RateLimitConfig.vue";
	import UIConfigPanel from "./UIConfigPanel.vue";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: boolean;
		workflowId: string;
		workflowName: string;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: boolean): void;
		(e: "create-success"): void;
	}>();

	const show = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const loading = ref(false);
	const title = ref("");
	const description = ref("");
	const billTo = ref<"sharer" | "runner">("sharer");
	const expiresAt = ref<string>("");
	const rateLimit = ref({ ...DEFAULT_RATE_LIMIT });
	const uiConfig = ref({ ...DEFAULT_UI_CONFIG });

	const resetForm = () => {
		title.value = "";
		description.value = "";
		billTo.value = "sharer";
		expiresAt.value = "";
		rateLimit.value = { ...DEFAULT_RATE_LIMIT };
		uiConfig.value = { ...DEFAULT_UI_CONFIG };
	};

	const handleCreate = async () => {
		if (!title.value.trim()) {
			Error(t("error"), t("shares.title_required"));
			return;
		}

		if (
			!uiConfig.value.allow_view &&
			!uiConfig.value.allow_run &&
			!uiConfig.value.allow_copy
		) {
			Error(t("error"), t("shares.at_least_one_permission"));
			return;
		}

		const createData: CreateShareRequest = {
			title: title.value.trim(),
			description: description.value.trim(),
			bill_to: billTo.value,
			rate_limit: rateLimit.value,
			ui_config: uiConfig.value,
		};

		if (expiresAt.value) {
			const expiryDate = new Date(expiresAt.value);
			if (expiryDate <= new Date()) {
				Error(t("error"), t("shares.expiry_date_future"));
				return;
			}
			createData.expires_at = Math.floor(expiryDate.getTime() / 1000);
		}

		try {
			loading.value = true;
			await SharesAPI.createShare(props.workflowId, createData);
			Success(t("success"), t("shares.create_share_success"));
			resetForm();
			emit("create-success");
		} catch (err) {
			Error(t("error"), t("shares.create_share_error"));
		} finally {
			loading.value = false;
		}
	};

	const handleCancel = () => {
		resetForm();
		show.value = false;
	};

	// Format date for input[type="datetime-local"]
	const formatDateTimeLocal = (date: Date) => {
		return date.toISOString().slice(0, 16);
	};

	// Set minimum date to current time
	const minDateTime = computed(() => {
		return formatDateTimeLocal(new Date());
	});
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div
				class="modal-box relative max-w-5xl h-[90vh] p-0 bg-gradient-to-br from-slate-50 to-blue-50/30"
			>
				<!-- 头部区域 -->
				<div
					class="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b border-slate-200 p-6"
				>
					<div class="flex justify-between items-center">
						<div class="flex items-center gap-3">
							<div
								class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg"
							>
								<ShareIcon class="w-5 h-5 text-white" />
							</div>
							<div>
								<h3 class="font-bold text-2xl text-slate-800">
									{{ t("shares.create_share") }}
								</h3>
								<p class="text-sm text-slate-600 mt-1">
									{{ t("shares.create_new_share_desc") }}
								</p>
							</div>
						</div>
						<button
							class="btn btn-ghost btn-circle btn-sm hover:bg-slate-100"
							@click="handleCancel"
						>
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>
				</div>

				<!-- 内容区域 -->
				<div class="p-6 h-[calc(100%-140px)] overflow-y-auto">
					<div class="space-y-6">
						<!-- Basic Information -->
						<div
							class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 overflow-hidden"
						>
							<div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4">
								<h4
									class="text-lg font-semibold text-white flex items-center gap-2"
								>
									<svg
										class="w-5 h-5"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
										></path>
									</svg>
									{{ t("shares.basic_info") }}
								</h4>
							</div>
							<div class="p-6 space-y-6">
								<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
									<div class="form-control">
										<label class="label">
											<span class="label-text font-medium text-slate-700"
												>{{ t("shares.share_title") }} *</span
											>
										</label>
										<input
											v-model="title"
											type="text"
											class="input input-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
											:placeholder="t('shares.title_placeholder')"
										/>
									</div>

									<div class="form-control">
										<label class="label">
											<span
												class="label-text font-medium text-slate-700"
												>{{ t("shares.billing_method") }}</span
											>
										</label>
										<select
											v-model="billTo"
											class="select select-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500"
										>
											<option value="sharer">
												{{ t("shares.sharer_pays") }}
											</option>
											<option value="runner">
												{{ t("shares.runner_pays") }}
											</option>
										</select>
										<label class="label">
											<span class="label-text-alt text-slate-500">
												{{
													billTo === "sharer"
														? t("shares.sharer_pays_desc")
														: t("shares.runner_pays_desc")
												}}
											</span>
										</label>
									</div>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text font-medium text-slate-700">{{
											t("shares.share_description")
										}}</span>
									</label>
									<textarea
										v-model="description"
										class="textarea textarea-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
										:placeholder="t('shares.description_placeholder')"
										rows="3"
									></textarea>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text font-medium text-slate-700">{{
											t("shares.expires_at")
										}}</span>
									</label>
									<input
										v-model="expiresAt"
										type="datetime-local"
										class="input input-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
										:min="minDateTime"
									/>
									<label class="label">
										<span class="label-text-alt text-slate-500">{{
											t("shares.expires_at_desc")
										}}</span>
									</label>
								</div>
							</div>
						</div>

						<!-- UI Configuration -->
						<div
							class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 overflow-hidden"
						>
							<div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4">
								<h4
									class="text-lg font-semibold text-white flex items-center gap-2"
								>
									<svg
										class="w-5 h-5"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
										></path>
									</svg>
									{{ t("shares.ui_configuration") }}
								</h4>
							</div>
							<div class="p-6">
								<UIConfigPanel v-model="uiConfig" />
							</div>
						</div>

						<!-- Rate Limiting -->
						<div
							class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 overflow-hidden"
						>
							<div class="bg-gradient-to-r from-amber-500 to-amber-600 p-4">
								<h4
									class="text-lg font-semibold text-white flex items-center gap-2"
								>
									<svg
										class="w-5 h-5"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
										></path>
									</svg>
									{{ t("shares.rate_limiting") }}
								</h4>
							</div>
							<div class="p-6">
								<RateLimitConfig v-model="rateLimit" />
							</div>
						</div>
					</div>
				</div>

				<!-- 底部操作区域 -->
				<div
					class="sticky bottom-0 bg-white/95 backdrop-blur-sm border-t border-slate-200 p-6"
				>
					<div class="flex justify-end gap-3">
						<button class="btn-modern" @click="handleCancel" :disabled="loading">
							{{ t("shares.cancel") }}
						</button>
						<button
							class="btn-gradient-primary gap-2"
							@click="handleCreate"
							:disabled="loading"
						>
							<span
								v-if="loading"
								class="loading loading-spinner loading-sm"
							></span>
							<ShareIcon v-else class="w-4 h-4" />
							{{ t("shares.create") }}
						</button>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="handleCancel"></label>
		</div>
	</Teleport>
</template>
