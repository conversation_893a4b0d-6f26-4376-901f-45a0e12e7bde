<script setup lang="ts">
	import { ref, computed, watch } from "vue";
	import { useI18n } from "vue-i18n";
	import { Error, Success } from "@/utils/notify";
	import { SharesAPI, type UpdateShareRequest, type WorkflowShare } from "@/api/shares";
	import { XMarkIcon, PencilIcon } from "@heroicons/vue/24/outline";
	import RateLimitConfig from "./RateLimitConfig.vue";
	import UIConfigPanel from "./UIConfigPanel.vue";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: boolean;
		workflowId: string;
		share: WorkflowShare | null;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: boolean): void;
		(e: "edit-success"): void;
	}>();

	const show = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const loading = ref(false);
	const title = ref("");
	const description = ref("");
	const billTo = ref<"sharer" | "runner">("sharer");
	const isEnabled = ref(true);
	const expiresAt = ref<string>("");
	const rateLimit = ref({
		enabled: false,
		daily_run_limit: 100,
		hourly_run_limit: 10,
		per_ip_daily_limit: 20,
		cooldown_seconds: 5,
	});
	const uiConfig = ref({
		allow_view: true,
		allow_run: true,
		allow_copy: false,
		show_workflow_graph: true,
		welcome_message: "",
		custom_theme: {},
	});

	const loadShareData = () => {
		if (!props.share) return;

		title.value = props.share.title;
		description.value = props.share.description;
		billTo.value = props.share.bill_to;
		isEnabled.value = props.share.is_enabled;
		expiresAt.value = props.share.expires_at
			? new Date(props.share.expires_at).toISOString().slice(0, 16)
			: "";
		rateLimit.value = { ...props.share.rate_limit };
		uiConfig.value = { ...props.share.ui_config };
	};

	const handleUpdate = async () => {
		if (!props.share) return;

		if (!title.value.trim()) {
			Error(t("error"), t("shares.title_required"));
			return;
		}

		if (
			!uiConfig.value.allow_view &&
			!uiConfig.value.allow_run &&
			!uiConfig.value.allow_copy
		) {
			Error(t("error"), t("shares.at_least_one_permission"));
			return;
		}

		const updateData: UpdateShareRequest = {
			title: title.value.trim(),
			description: description.value.trim(),
			bill_to: billTo.value,
			is_enabled: isEnabled.value,
			rate_limit: rateLimit.value,
			ui_config: uiConfig.value,
		};

		if (expiresAt.value) {
			const expiryDate = new Date(expiresAt.value);
			if (expiryDate <= new Date()) {
				Error(t("error"), t("shares.expiry_date_future"));
				return;
			}
			updateData.expires_at = Math.floor(expiryDate.getTime() / 1000);
		} else {
			updateData.expires_at = 0; // Remove expiry
		}

		try {
			loading.value = true;
			await SharesAPI.updateShare(props.workflowId, props.share.share_uuid, updateData);
			Success(t("success"), t("shares.update_share_success"));
			emit("edit-success");
		} catch (err) {
			Error(t("error"), t("shares.update_share_error"));
		} finally {
			loading.value = false;
		}
	};

	const handleCancel = () => {
		show.value = false;
	};

	// Format date for input[type="datetime-local"]
	const formatDateTimeLocal = (date: Date) => {
		return date.toISOString().slice(0, 16);
	};

	// Set minimum date to current time
	const minDateTime = computed(() => {
		return formatDateTimeLocal(new Date());
	});

	// Get share URL with safe window.location check
	const getShareUrl = (shareUuid: string) => {
		if (typeof window !== "undefined" && window.location) {
			return `${window.location.origin}/shared/${shareUuid}`;
		}
		return `/shared/${shareUuid}`;
	};

	// Watch for share changes and reload data
	watch(
		() => props.share,
		() => {
			loadShareData();
		},
		{ immediate: true }
	);

	// Watch for modal open/close
	watch(
		() => show.value,
		(newVal) => {
			if (newVal) {
				loadShareData();
			}
		}
	);
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div
				class="modal-box relative max-w-5xl h-[90vh] p-0 bg-gradient-to-br from-slate-50 to-blue-50/30"
			>
				<div class="flex flex-col h-full" v-if="share">
					<!-- 头部区域 -->
					<div
						class="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b border-slate-200 p-6"
					>
						<div class="flex justify-between items-center">
							<div class="flex items-center gap-3">
								<div
									class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg"
								>
									<PencilIcon class="w-5 h-5 text-white" />
								</div>
								<div>
									<h3 class="font-bold text-2xl text-slate-800">
										{{ t("shares.edit_share") }}
									</h3>
									<p class="text-sm text-slate-600 mt-1">
										{{ share.title }}
									</p>
								</div>
							</div>
							<button
								class="btn btn-ghost btn-circle btn-sm hover:bg-slate-100"
								@click="handleCancel"
							>
								<XMarkIcon class="w-5 h-5" />
							</button>
						</div>
					</div>

					<!-- 内容区域 -->
					<div class="flex-1 p-6 overflow-y-auto">
						<div class="space-y-6">
							<!-- Basic Information -->
							<div
								class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 overflow-hidden"
							>
								<div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4">
									<h4
										class="text-lg font-semibold text-white flex items-center gap-2"
									>
										<svg
											class="w-5 h-5"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
											></path>
										</svg>
										{{ t("shares.basic_info") }}
									</h4>
								</div>
								<div class="p-6 space-y-6">
									<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
										<div class="form-control">
											<label class="label">
												<span
													class="label-text font-medium text-slate-700"
													>{{ t("shares.share_title") }} *</span
												>
											</label>
											<input
												v-model="title"
												type="text"
												class="input input-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
												:placeholder="t('shares.title_placeholder')"
											/>
										</div>

										<div class="form-control">
											<label class="label">
												<span
													class="label-text font-medium text-slate-700"
													>{{ t("shares.billing_method") }}</span
												>
											</label>
											<select
												v-model="billTo"
												class="select select-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500"
											>
												<option value="sharer">
													{{ t("shares.sharer_pays") }}
												</option>
												<option value="runner">
													{{ t("shares.runner_pays") }}
												</option>
											</select>
											<label class="label">
												<span class="label-text-alt text-slate-500">
													{{
														billTo === "sharer"
															? t("shares.sharer_pays_desc")
															: t("shares.runner_pays_desc")
													}}
												</span>
											</label>
										</div>
									</div>

									<div class="form-control">
										<label class="label">
											<span
												class="label-text font-medium text-slate-700"
												>{{ t("shares.share_description") }}</span
											>
										</label>
										<textarea
											v-model="description"
											class="textarea textarea-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
											:placeholder="t('shares.description_placeholder')"
											rows="3"
										></textarea>
									</div>

									<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
										<div
											class="bg-slate-50 rounded-xl p-4 border border-slate-200"
										>
											<div class="flex items-center justify-between">
												<div class="flex items-center gap-3">
													<div
														class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"
													>
														<svg
															class="w-4 h-4 text-green-600"
															fill="none"
															stroke="currentColor"
															viewBox="0 0 24 24"
														>
															<path
																stroke-linecap="round"
																stroke-linejoin="round"
																stroke-width="2"
																d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
															></path>
														</svg>
													</div>
													<div>
														<p class="font-medium text-slate-800">
															{{ t("shares.is_enabled") }}
														</p>
														<p class="text-xs text-slate-500">
															{{ t("shares.is_enabled_desc") }}
														</p>
													</div>
												</div>
												<input
													type="checkbox"
													class="toggle toggle-success"
													v-model="isEnabled"
												/>
											</div>
										</div>

										<div class="form-control">
											<label class="label">
												<span
													class="label-text font-medium text-slate-700"
													>{{ t("shares.expires_at") }}</span
												>
											</label>
											<input
												v-model="expiresAt"
												type="datetime-local"
												class="input input-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
												:min="minDateTime"
											/>
											<label class="label">
												<span class="label-text-alt text-slate-500">{{
													t("shares.expires_at_desc")
												}}</span>
											</label>
										</div>
									</div>
								</div>
							</div>

							<!-- UI Configuration -->
							<div
								class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 overflow-hidden"
							>
								<div
									class="bg-gradient-to-r from-purple-500 to-purple-600 p-4"
								>
									<h4
										class="text-lg font-semibold text-white flex items-center gap-2"
									>
										<svg
											class="w-5 h-5"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
											></path>
										</svg>
										{{ t("shares.ui_configuration") }}
									</h4>
								</div>
								<div class="p-6">
									<UIConfigPanel v-model="uiConfig" />
								</div>
							</div>

							<!-- Rate Limiting -->
							<div
								class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 overflow-hidden"
							>
								<div class="bg-gradient-to-r from-amber-500 to-amber-600 p-4">
									<h4
										class="text-lg font-semibold text-white flex items-center gap-2"
									>
										<svg
											class="w-5 h-5"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
											></path>
										</svg>
										{{ t("shares.rate_limiting") }}
									</h4>
								</div>
								<div class="p-6">
									<RateLimitConfig v-model="rateLimit" />
								</div>
							</div>

							<!-- Share Info -->
							<div
								class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-md border border-slate-200 overflow-hidden"
							>
								<div class="bg-gradient-to-r from-slate-500 to-slate-600 p-4">
									<h4
										class="text-lg font-semibold text-white flex items-center gap-2"
									>
										<svg
											class="w-5 h-5"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
											></path>
										</svg>
										{{ t("shares.share_info") }}
									</h4>
								</div>
								<div class="p-6">
									<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
										<div
											class="bg-slate-50 rounded-xl p-4 border border-slate-200"
										>
											<div class="flex items-center gap-2 mb-2">
												<svg
													class="w-4 h-4 text-blue-600"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
													></path>
												</svg>
												<span class="font-medium text-slate-700">{{
													t("shares.share_uuid")
												}}</span>
											</div>
											<p
												class="font-mono text-sm text-slate-600 bg-white p-2 rounded border break-all"
											>
												{{ share.share_uuid }}
											</p>
										</div>
										<div
											class="bg-slate-50 rounded-xl p-4 border border-slate-200"
										>
											<div class="flex items-center gap-2 mb-2">
												<svg
													class="w-4 h-4 text-green-600"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
													></path>
												</svg>
												<span class="font-medium text-slate-700">{{
													t("shares.created_at")
												}}</span>
											</div>
											<p class="text-sm text-slate-600">
												{{
													new Date(share.created_at).toLocaleString()
												}}
											</p>
										</div>
										<div
											class="bg-slate-50 rounded-xl p-4 border border-slate-200"
										>
											<div class="flex items-center gap-2 mb-2">
												<svg
													class="w-4 h-4 text-amber-600"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
													></path>
												</svg>
												<span class="font-medium text-slate-700">{{
													t("shares.updated_at")
												}}</span>
											</div>
											<p class="text-sm text-slate-600">
												{{
													new Date(share.updated_at).toLocaleString()
												}}
											</p>
										</div>
										<div
											class="bg-slate-50 rounded-xl p-4 border border-slate-200"
										>
											<div class="flex items-center gap-2 mb-2">
												<svg
													class="w-4 h-4 text-purple-600"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
													></path>
												</svg>
												<span class="font-medium text-slate-700">{{
													t("shares.share_link")
												}}</span>
											</div>
											<a
												:href="`/shared/${share.share_uuid}`"
												target="_blank"
												class="text-sm text-blue-600 hover:text-blue-800 underline break-all"
											>
												{{ getShareUrl(share.share_uuid) }}
											</a>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 底部操作区域 -->
					<div
						class="sticky bottom-0 bg-white/95 backdrop-blur-sm border-t border-slate-200 p-6"
					>
						<div class="flex justify-end gap-3">
							<button
								class="btn-modern"
								@click="handleCancel"
								:disabled="loading"
							>
								{{ t("shares.cancel") }}
							</button>
							<button
								class="btn-gradient-primary gap-2"
								@click="handleUpdate"
								:disabled="loading"
							>
								<span
									v-if="loading"
									class="loading loading-spinner loading-sm"
								></span>
								<PencilIcon v-else class="w-4 h-4" />
								{{ t("shares.update") }}
							</button>
						</div>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="handleCancel"></label>
		</div>
	</Teleport>
</template>
