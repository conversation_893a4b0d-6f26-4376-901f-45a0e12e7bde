<script setup lang="ts">
	import { ref, computed, watch } from "vue";
	import { useI18n } from "vue-i18n";
	import { Error, Success } from "@/utils/notify";
	import { SharesAPI, type UpdateShareRequest, type WorkflowShare } from "@/api/shares";
	import { XMarkIcon, PencilIcon } from "@heroicons/vue/24/outline";
	import RateLimitConfig from "./RateLimitConfig.vue";
	import UIConfigPanel from "./UIConfigPanel.vue";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: boolean;
		workflowId: string;
		share: WorkflowShare | null;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: boolean): void;
		(e: "edit-success"): void;
	}>();

	const show = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const loading = ref(false);
	const title = ref("");
	const description = ref("");
	const billTo = ref<"sharer" | "runner">("sharer");
	const isEnabled = ref(true);
	const expiresAt = ref<string>("");
	const rateLimit = ref({
		enabled: false,
		daily_run_limit: 100,
		hourly_run_limit: 10,
		per_ip_daily_limit: 20,
		cooldown_seconds: 5,
	});
	const uiConfig = ref({
		allow_view: true,
		allow_run: true,
		allow_copy: false,
		show_workflow_graph: true,
		welcome_message: "",
		custom_theme: {},
	});

	const loadShareData = () => {
		if (!props.share) return;

		title.value = props.share.title;
		description.value = props.share.description;
		billTo.value = props.share.bill_to;
		isEnabled.value = props.share.is_enabled;
		expiresAt.value = props.share.expires_at
			? new Date(props.share.expires_at).toISOString().slice(0, 16)
			: "";
		rateLimit.value = { ...props.share.rate_limit };
		uiConfig.value = { ...props.share.ui_config };
	};

	const handleUpdate = async () => {
		if (!props.share) return;

		if (!title.value.trim()) {
			Error(t("error"), t("shares.title_required"));
			return;
		}

		if (
			!uiConfig.value.allow_view &&
			!uiConfig.value.allow_run &&
			!uiConfig.value.allow_copy
		) {
			Error(t("error"), t("shares.at_least_one_permission"));
			return;
		}

		const updateData: UpdateShareRequest = {
			title: title.value.trim(),
			description: description.value.trim(),
			bill_to: billTo.value,
			is_enabled: isEnabled.value,
			rate_limit: rateLimit.value,
			ui_config: uiConfig.value,
		};

		if (expiresAt.value) {
			const expiryDate = new Date(expiresAt.value);
			if (expiryDate <= new Date()) {
				Error(t("error"), t("shares.expiry_date_future"));
				return;
			}
			updateData.expires_at = Math.floor(expiryDate.getTime() / 1000);
		} else {
			updateData.expires_at = 0; // Remove expiry
		}

		try {
			loading.value = true;
			await SharesAPI.updateShare(props.workflowId, props.share.share_uuid, updateData);
			Success(t("success"), t("shares.update_share_success"));
			emit("edit-success");
		} catch (err) {
			Error(t("error"), t("shares.update_share_error"));
		} finally {
			loading.value = false;
		}
	};

	const handleCancel = () => {
		show.value = false;
	};

	// Format date for input[type="datetime-local"]
	const formatDateTimeLocal = (date: Date) => {
		return date.toISOString().slice(0, 16);
	};

	// Set minimum date to current time
	const minDateTime = computed(() => {
		return formatDateTimeLocal(new Date());
	});

	// Get share URL with safe window.location check
	const getShareUrl = (shareUuid: string) => {
		if (typeof window !== "undefined" && window.location) {
			return `${window.location.origin}/shared/${shareUuid}`;
		}
		return `/shared/${shareUuid}`;
	};

	// Watch for share changes and reload data
	watch(
		() => props.share,
		() => {
			loadShareData();
		},
		{ immediate: true }
	);

	// Watch for modal open/close
	watch(
		() => show.value,
		(newVal) => {
			if (newVal) {
				loadShareData();
			}
		}
	);
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div class="modal-box relative max-w-4xl">
				<div class="flex flex-col space-y-6" v-if="share">
					<div class="flex justify-between items-center">
						<h3 class="font-bold text-2xl">{{ t("shares.edit_share") }}</h3>
						<button class="btn btn-ghost btn-circle btn-sm" @click="handleCancel">
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<div class="flex flex-col space-y-4">
						<!-- Basic Information -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<h4 class="card-title text-lg">
									{{ t("shares.basic_info") }}
								</h4>

								<div class="form-control">
									<label class="label">
										<span class="label-text"
											>{{ t("shares.share_title") }} *</span
										>
									</label>
									<input
										v-model="title"
										type="text"
										class="input input-bordered w-full"
										:placeholder="t('shares.title_placeholder')"
									/>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text">{{
											t("shares.share_description")
										}}</span>
									</label>
									<textarea
										v-model="description"
										class="textarea textarea-bordered w-full"
										:placeholder="t('shares.description_placeholder')"
										rows="3"
									></textarea>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text">{{
											t("shares.billing_method")
										}}</span>
									</label>
									<select
										v-model="billTo"
										class="select select-bordered w-full"
									>
										<option value="sharer">
											{{ t("shares.sharer_pays") }}
										</option>
										<option value="runner">
											{{ t("shares.runner_pays") }}
										</option>
									</select>
									<label class="label">
										<span class="label-text-alt">
											{{
												billTo === "sharer"
													? t("shares.sharer_pays_desc")
													: t("shares.runner_pays_desc")
											}}
										</span>
									</label>
								</div>

								<div class="form-control">
									<label class="label cursor-pointer">
										<span class="label-text">{{
											t("shares.is_enabled")
										}}</span>
										<input
											type="checkbox"
											class="toggle toggle-primary"
											v-model="isEnabled"
										/>
									</label>
									<label class="label">
										<span class="label-text-alt">{{
											t("shares.is_enabled_desc")
										}}</span>
									</label>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text">{{
											t("shares.expires_at")
										}}</span>
									</label>
									<input
										v-model="expiresAt"
										type="datetime-local"
										class="input input-bordered w-full"
										:min="minDateTime"
									/>
									<label class="label">
										<span class="label-text-alt">{{
											t("shares.expires_at_desc")
										}}</span>
									</label>
								</div>
							</div>
						</div>

						<!-- UI Configuration -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<h4 class="card-title text-lg">
									{{ t("shares.ui_configuration") }}
								</h4>
								<UIConfigPanel v-model="uiConfig" />
							</div>
						</div>

						<!-- Rate Limiting -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<h4 class="card-title text-lg">
									{{ t("shares.rate_limiting") }}
								</h4>
								<RateLimitConfig v-model="rateLimit" />
							</div>
						</div>

						<!-- Share Info -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<h4 class="card-title text-lg">
									{{ t("shares.share_info") }}
								</h4>
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
									<div>
										<span class="font-semibold"
											>{{ t("shares.share_uuid") }}:</span
										>
										<span class="font-mono">{{ share.share_uuid }}</span>
									</div>
									<div>
										<span class="font-semibold"
											>{{ t("shares.created_at") }}:</span
										>
										<span>{{
											new Date(share.created_at).toLocaleString()
										}}</span>
									</div>
									<div>
										<span class="font-semibold"
											>{{ t("shares.updated_at") }}:</span
										>
										<span>{{
											new Date(share.updated_at).toLocaleString()
										}}</span>
									</div>
									<div>
										<span class="font-semibold"
											>{{ t("shares.share_link") }}:</span
										>
										<a
											:href="`/shared/${share.share_uuid}`"
											target="_blank"
											class="link link-primary"
										>
											{{ getShareUrl(share.share_uuid) }}
										</a>
									</div>
								</div>
							</div>
						</div>

						<!-- Actions -->
						<div class="flex justify-end space-x-2">
							<button
								class="btn btn-ghost"
								@click="handleCancel"
								:disabled="loading"
							>
								{{ t("shares.cancel") }}
							</button>
							<button
								class="btn btn-primary"
								@click="handleUpdate"
								:disabled="loading"
							>
								<span
									v-if="loading"
									class="loading loading-spinner loading-sm mr-2"
								></span>
								{{ t("shares.update") }}
							</button>
						</div>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="handleCancel"></label>
		</div>
	</Teleport>
</template>
