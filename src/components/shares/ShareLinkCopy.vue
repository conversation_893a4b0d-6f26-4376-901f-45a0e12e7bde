<script setup lang="ts">
	import { ref } from "vue";
	import { useI18n } from "vue-i18n";
	import { Success } from "@/utils/notify";
	import { ClipboardIcon, CheckIcon } from "@heroicons/vue/24/outline";

	const { t } = useI18n();

	const props = defineProps<{
		shareUuid: string;
	}>();

	const copied = ref(false);

	const getShareUrl = () => {
		if (typeof window !== "undefined" && window.location) {
			return `${window.location.origin}/dashboard/shared/${props.shareUuid}`;
		}
		return `/dashboard/shared/${props.shareUuid}`;
	};

	const shareUrl = getShareUrl();

	const copyToClipboard = async () => {
		try {
			await navigator.clipboard.writeText(shareUrl);
			copied.value = true;
			Success(t("success"), t("shares.link_copied"));

			// Reset copied state after 2 seconds
			setTimeout(() => {
				copied.value = false;
			}, 2000);
		} catch (err) {
			// Fallback for browsers that don't support clipboard API
			const textArea = document.createElement("textarea");
			textArea.value = shareUrl;
			document.body.appendChild(textArea);
			textArea.focus();
			textArea.select();
			document.execCommand("copy");
			document.body.removeChild(textArea);

			copied.value = true;
			Success(t("success"), t("shares.link_copied"));

			setTimeout(() => {
				copied.value = false;
			}, 2000);
		}
	};
</script>

<template>
	<div class="dropdown dropdown-left">
		<div
			tabindex="0"
			role="button"
			class="btn-outline-primary btn-sm gap-2 transition-all duration-200"
			:class="{ 'btn-outline-success': copied }"
		>
			<component :is="copied ? CheckIcon : ClipboardIcon" class="w-4 h-4" />
			{{ copied ? t("shares.copied") : t("shares.copy_link") }}
		</div>
		<div
			tabindex="0"
			class="dropdown-content z-[1] bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 w-96 p-4"
		>
			<div class="space-y-3">
				<div class="flex items-center gap-2 mb-3">
					<div
						class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"
					>
						<ClipboardIcon class="w-4 h-4 text-blue-600" />
					</div>
					<div>
						<p class="font-medium text-slate-800">{{ t("shares.share_link") }}</p>
						<p class="text-xs text-slate-500">{{ t("shares.share_link_desc") }}</p>
					</div>
				</div>
				<div class="flex items-center gap-2">
					<input
						type="text"
						:value="shareUrl"
						readonly
						class="input input-bordered input-sm flex-1 font-mono text-xs bg-slate-50 border-slate-300 focus:border-blue-500"
					/>
					<button
						class="btn-sm transition-all duration-200"
						:class="copied ? 'btn-gradient-success' : 'btn-gradient-primary'"
						@click="copyToClipboard"
					>
						<component :is="copied ? CheckIcon : ClipboardIcon" class="w-4 h-4" />
					</button>
				</div>
				<p class="text-xs text-slate-500 mt-2">
					{{ t("shares.click_to_copy") }}
				</p>
			</div>
		</div>
	</div>
</template>
