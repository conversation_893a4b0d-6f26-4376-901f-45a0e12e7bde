<script setup lang="ts">
	import { computed } from "vue";
	import { useI18n } from "vue-i18n";
	import type { ShareRateLimit } from "@/api/shares";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: ShareRateLimit;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: ShareRateLimit): void;
	}>();

	const rateLimit = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const updateField = (field: keyof ShareRateLimit, value: any) => {
		rateLimit.value = {
			...rateLimit.value,
			[field]: value,
		};
	};

	const formatTime = (seconds: number) => {
		if (seconds < 60) return `${seconds}s`;
		if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
		return `${Math.floor(seconds / 3600)}h`;
	};
</script>

<template>
	<div class="space-y-6">
		<!-- Enable Rate Limiting -->
		<div class="bg-slate-50 rounded-xl p-4 border border-slate-200">
			<div class="flex items-center justify-between mb-2">
				<div class="flex items-center gap-3">
					<div
						class="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center"
					>
						<svg
							class="w-4 h-4 text-amber-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
							></path>
						</svg>
					</div>
					<div>
						<p class="font-medium text-slate-800">
							{{ t("shares.enable_rate_limiting") }}
						</p>
						<p class="text-xs text-slate-500">
							{{ t("shares.rate_limiting_desc") }}
						</p>
					</div>
				</div>
				<input
					type="checkbox"
					class="toggle toggle-warning"
					:checked="rateLimit.enabled"
					@change="
						updateField('enabled', ($event.target as HTMLInputElement).checked)
					"
				/>
			</div>
		</div>

		<!-- Rate Limiting Settings -->
		<div v-if="rateLimit.enabled" class="space-y-4">
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Daily Run Limit -->
				<div class="bg-white/50 rounded-xl p-4 border border-slate-200">
					<label class="block">
						<div class="flex items-center gap-2 mb-2">
							<svg
								class="w-4 h-4 text-blue-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
								></path>
							</svg>
							<span class="font-medium text-slate-700">{{
								t("shares.daily_run_limit")
							}}</span>
						</div>
						<input
							type="number"
							class="input input-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
							min="0"
							max="10000"
							:value="rateLimit.daily_run_limit"
							@input="
								updateField(
									'daily_run_limit',
									parseInt(($event.target as HTMLInputElement).value) || 0
								)
							"
						/>
						<p class="text-xs text-slate-500 mt-1">
							{{ t("shares.daily_run_limit_desc") }}
						</p>
					</label>
				</div>

				<!-- Hourly Run Limit -->
				<div class="bg-white/50 rounded-xl p-4 border border-slate-200">
					<label class="block">
						<div class="flex items-center gap-2 mb-2">
							<svg
								class="w-4 h-4 text-green-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
								></path>
							</svg>
							<span class="font-medium text-slate-700">{{
								t("shares.hourly_run_limit")
							}}</span>
						</div>
						<input
							type="number"
							class="input input-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
							min="0"
							max="1000"
							:value="rateLimit.hourly_run_limit"
							@input="
								updateField(
									'hourly_run_limit',
									parseInt(($event.target as HTMLInputElement).value) || 0
								)
							"
						/>
						<p class="text-xs text-slate-500 mt-1">
							{{ t("shares.hourly_run_limit_desc") }}
						</p>
					</label>
				</div>

				<!-- Per IP Daily Limit -->
				<div class="bg-white/50 rounded-xl p-4 border border-slate-200">
					<label class="block">
						<div class="flex items-center gap-2 mb-2">
							<svg
								class="w-4 h-4 text-purple-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
								></path>
							</svg>
							<span class="font-medium text-slate-700">{{
								t("shares.per_ip_daily_limit")
							}}</span>
						</div>
						<input
							type="number"
							class="input input-bordered w-full bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
							min="0"
							max="1000"
							:value="rateLimit.per_ip_daily_limit"
							@input="
								updateField(
									'per_ip_daily_limit',
									parseInt(($event.target as HTMLInputElement).value) || 0
								)
							"
						/>
						<p class="text-xs text-slate-500 mt-1">
							{{ t("shares.per_ip_daily_limit_desc") }}
						</p>
					</label>
				</div>

				<!-- Cooldown Seconds -->
				<div class="bg-white/50 rounded-xl p-4 border border-slate-200">
					<label class="block">
						<div class="flex items-center gap-2 mb-2">
							<svg
								class="w-4 h-4 text-red-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"
								></path>
							</svg>
							<span class="font-medium text-slate-700">{{
								t("shares.cooldown_seconds")
							}}</span>
						</div>
						<div class="flex items-center gap-2">
							<input
								type="number"
								class="input input-bordered flex-1 bg-white/50 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
								min="0"
								max="3600"
								:value="rateLimit.cooldown_seconds"
								@input="
									updateField(
										'cooldown_seconds',
										parseInt(($event.target as HTMLInputElement).value) ||
											0
									)
								"
							/>
							<span
								class="px-2 py-1 bg-slate-100 rounded text-xs font-mono text-slate-600"
							>
								{{ formatTime(rateLimit.cooldown_seconds) }}
							</span>
						</div>
						<p class="text-xs text-slate-500 mt-1">
							{{ t("shares.cooldown_seconds_desc") }}
						</p>
					</label>
				</div>
			</div>

			<!-- Rate Limiting Tips -->
			<div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
				<div class="flex items-start gap-3">
					<div
						class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0"
					>
						<svg
							class="w-4 h-4 text-blue-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							></path>
						</svg>
					</div>
					<div>
						<h4 class="font-semibold text-blue-800 mb-2">
							{{ t("shares.rate_limiting_tips") }}
						</h4>
						<ul class="text-sm text-blue-700 space-y-1">
							<li class="flex items-start gap-2">
								<span class="text-blue-500 mt-1">•</span>
								<span>{{ t("shares.rate_limit_tip_1") }}</span>
							</li>
							<li class="flex items-start gap-2">
								<span class="text-blue-500 mt-1">•</span>
								<span>{{ t("shares.rate_limit_tip_2") }}</span>
							</li>
							<li class="flex items-start gap-2">
								<span class="text-blue-500 mt-1">•</span>
								<span>{{ t("shares.rate_limit_tip_3") }}</span>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>

		<!-- Rate Limiting Disabled Info -->
		<div v-else class="bg-amber-50 border border-amber-200 rounded-xl p-4">
			<div class="flex items-start gap-3">
				<div
					class="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center flex-shrink-0"
				>
					<svg
						class="w-4 h-4 text-amber-600"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L8.732 15.5c-.77.833.192 2.5 1.732 2.5z"
						></path>
					</svg>
				</div>
				<div>
					<h4 class="font-semibold text-amber-800 mb-1">
						{{ t("shares.rate_limiting_disabled") }}
					</h4>
					<p class="text-sm text-amber-700">
						{{ t("shares.rate_limiting_disabled_desc") }}
					</p>
				</div>
			</div>
		</div>
	</div>
</template>
